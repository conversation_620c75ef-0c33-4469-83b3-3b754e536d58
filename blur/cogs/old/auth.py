import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Bot owners list
owners = [
    int(os.getenv('DISCORD_OWNER_ID', 1243888419403206660)),
    1386902885580669002,  # Your Discord ID
    1243888419403206660,  # Additional owner ID
]

# Bot configuration
BOT_TOKEN = os.getenv('DISCORD_TOKEN')
DATABASE_URL = f"mysql://{os.getenv('DB_USER')}:{os.getenv('DB_PASSWORD')}@{os.getenv('DB_HOST')}:{os.getenv('DB_PORT')}/{os.getenv('DB_NAME')}"

# Database connection details (for aiomysql)
DB_HOST = os.getenv('DB_HOST')
DB_PORT = int(os.getenv('DB_PORT', 3306))
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_NAME = os.getenv('DB_NAME')

# API Keys (optional)
SPOTIFY_CLIENT_ID = "your_spotify_client_id"
SPOTIFY_CLIENT_SECRET = "your_spotify_client_secret"
PIXIAN_API_KEY = os.getenv('PIXIAN_API_KEY', 'your_pixian_api_key')

# Bot settings
DEFAULT_PREFIX = os.getenv('DISCORD_PREFIX', ',')
BOT_COLOR = 0x2B2D31  # Discord's dark theme color

# Feature toggles
ENABLE_MUSIC = True
ENABLE_ECONOMY = False
ENABLE_LEVELING = False
ENABLE_AUTOMOD = True

# Logging settings
LOG_LEVEL = "INFO"
LOG_FILE = "bot.log"

# Database table schemas (for reference)
DATABASE_SCHEMA = """
-- Prefixes table
CREATE TABLE IF NOT EXISTS prefixes (
    guild_id BIGINT PRIMARY KEY,
    prefix TEXT NOT NULL DEFAULT ';'
);

-- Moderation table
CREATE TABLE IF NOT EXISTS mod (
    guild_id BIGINT PRIMARY KEY,
    channel_id BIGINT,
    jail_id BIGINT,
    role_id BIGINT
);

-- Cases table
CREATE TABLE IF NOT EXISTS cases (
    guild_id BIGINT PRIMARY KEY,
    count INTEGER DEFAULT 0
);

-- Donor table
CREATE TABLE IF NOT EXISTS donor (
    user_id BIGINT PRIMARY KEY,
    time INTEGER
);

-- Blacklist table
CREATE TABLE IF NOT EXISTS blacklist (
    user_id BIGINT PRIMARY KEY,
    reason TEXT
);

-- Welcome config table
CREATE TABLE IF NOT EXISTS welcome_config (
    guild_id BIGINT PRIMARY KEY,
    enabled BOOLEAN DEFAULT FALSE,
    channel_id BIGINT,
    message TEXT,
    embed BOOLEAN DEFAULT TRUE
);

-- Goodbye config table
CREATE TABLE IF NOT EXISTS goodbye_config (
    guild_id BIGINT PRIMARY KEY,
    enabled BOOLEAN DEFAULT FALSE,
    channel_id BIGINT,
    message TEXT,
    embed BOOLEAN DEFAULT TRUE
);

-- Guild config table
CREATE TABLE IF NOT EXISTS guild_config (
    guild_id BIGINT PRIMARY KEY,
    log_channel BIGINT,
    mute_role BIGINT,
    autorole BIGINT
);

-- Snipe messages table
CREATE TABLE IF NOT EXISTS snipe_messages (
    channel_id BIGINT PRIMARY KEY,
    author_id BIGINT,
    content TEXT,
    deleted_at TIMESTAMP
);

-- Edit snipe messages table
CREATE TABLE IF NOT EXISTS editsnipe_messages (
    channel_id BIGINT PRIMARY KEY,
    author_id BIGINT,
    before_content TEXT,
    after_content TEXT,
    edited_at TIMESTAMP
);

-- Command usage table
CREATE TABLE IF NOT EXISTS command_usage (
    id SERIAL PRIMARY KEY,
    command_name TEXT,
    user_id BIGINT,
    guild_id BIGINT,
    used_at TIMESTAMP
);

-- Whitelist table
CREATE TABLE IF NOT EXISTS whitelist (
    guild_id BIGINT,
    module TEXT,
    object_id BIGINT,
    mode TEXT,
    PRIMARY KEY (guild_id, module, object_id, mode)
);

-- Birthday table
CREATE TABLE IF NOT EXISTS birthday (
    user_id BIGINT PRIMARY KEY,
    bday DATE,
    state TEXT DEFAULT 'false'
);

-- Joint table (for fun commands)
CREATE TABLE IF NOT EXISTS joint (
    guild_id BIGINT PRIMARY KEY,
    holder BIGINT,
    hits INTEGER DEFAULT 0
);

-- Pot table (for fun commands)
CREATE TABLE IF NOT EXISTS pot (
    guild_id BIGINT PRIMARY KEY,
    holder BIGINT,
    hits INTEGER DEFAULT 0
);

-- Invoke table (for moderation logging)
CREATE TABLE IF NOT EXISTS invoke (
    guild_id BIGINT PRIMARY KEY,
    channel_id BIGINT
);

-- AntiNuke tables
CREATE TABLE IF NOT EXISTS antinuke_toggle (
    guild_id BIGINT PRIMARY KEY
);

CREATE TABLE IF NOT EXISTS antinuke (
    guild_id BIGINT,
    module TEXT,
    punishment TEXT,
    PRIMARY KEY (guild_id, module)
);

-- AutoMod tables
CREATE TABLE IF NOT EXISTS antispam (
    guild_id BIGINT PRIMARY KEY,
    punishment TEXT DEFAULT 'delete',
    seconds INTEGER DEFAULT 5
);

CREATE TABLE IF NOT EXISTS automod_config (
    guild_id BIGINT,
    module TEXT,
    enabled BOOLEAN DEFAULT FALSE,
    PRIMARY KEY (guild_id, module)
);

CREATE TABLE IF NOT EXISTS chatfilter (
    guild_id BIGINT,
    word TEXT,
    PRIMARY KEY (guild_id, word)
);

-- Economy tables
CREATE TABLE IF NOT EXISTS economy (
    user_id BIGINT PRIMARY KEY,
    cash DECIMAL(15,2) DEFAULT 100.00,
    bank DECIMAL(15,2) DEFAULT 0.00,
    rob INTEGER DEFAULT 0,
    daily INTEGER DEFAULT 0,
    weekly INTEGER DEFAULT 0,
    last_work INTEGER DEFAULT 0
);

-- Leveling tables
CREATE TABLE IF NOT EXISTS levels (
    guild_id BIGINT,
    user_id BIGINT,
    xp INTEGER DEFAULT 0,
    level INTEGER DEFAULT 0,
    PRIMARY KEY (guild_id, user_id)
);

CREATE TABLE IF NOT EXISTS leveling_config (
    guild_id BIGINT PRIMARY KEY,
    enabled BOOLEAN DEFAULT FALSE,
    channel_id BIGINT
);

CREATE TABLE IF NOT EXISTS level_roles (
    guild_id BIGINT,
    level INTEGER,
    role_id BIGINT,
    PRIMARY KEY (guild_id, level)
);
"""
