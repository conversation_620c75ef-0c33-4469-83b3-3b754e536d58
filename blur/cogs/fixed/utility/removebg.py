import discord
from discord.ext import commands
from tools.checks import Perms
from tools.ext import embed
from config.constants import Emojis
import aiohttp
import io


class RemoveBG(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    async def is_valid_image_url(self, url):
        """Check if URL is a valid image"""
        image_extensions = ('.png', '.jpg', '.jpeg', '.gif', '.webp', '.bmp')
        return any(url.lower().endswith(ext) for ext in image_extensions)

    async def download_image(self, url):
        """Download image from URL"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        return await response.read()
                    else:
                        return None
        except Exception:
            return None

    async def remove_background_free(self, image_data):
        """Remove background using Photoscissors free API"""
        try:
            async with aiohttp.ClientSession() as session:
                # Using Photoscissors API (actually free, no key needed)
                data = aiohttp.FormData()
                data.add_field('image', image_data, filename='image.png', content_type='image/png')

                # Photoscissors free API endpoint
                url = 'https://photoscissors.com/api/v1/cutout'

                async with session.post(url, data=data) as response:
                    if response.status == 200:
                        return await response.read()
                    else:
                        print(f"Photoscissors API Error: {response.status}")
                        # Try alternative API
                        return await self.try_slazzer_api(session, image_data)
        except Exception as e:
            print(f"Error removing background: {e}")
            return None

    async def try_slazzer_api(self, session, image_data):
        """Try Slazzer free API as backup"""
        try:
            # Using Slazzer free API
            data = aiohttp.FormData()
            data.add_field('source_image_file', image_data, filename='image.png', content_type='image/png')

            url = 'https://api.slazzer.com/v2.0/remove_image_background'

            async with session.post(url, data=data) as response:
                if response.status == 200:
                    result = await response.json()
                    if 'output_image_url' in result:
                        # Download the processed image
                        async with session.get(result['output_image_url']) as img_response:
                            if img_response.status == 200:
                                return await img_response.read()
                return None
        except Exception:
            return None

    @commands.command(
        name="removebg",
        aliases=["rbg"],
        description="Remove background from an image",
        usage="[image_url or attachment]"
    )
    @Perms.get_perms("send_messages")
    async def removebg(self, ctx, image_url: str = None):
        """Remove background from an image using free API services"""

        image_data = None
        
        # Check for attachment first
        if ctx.message.attachments:
            attachment = ctx.message.attachments[0]
            if attachment.content_type and attachment.content_type.startswith('image/'):
                try:
                    image_data = await attachment.read()
                except Exception:
                    return await embed.error(ctx, "Failed to read the attached image!")
            else:
                return await embed.error(ctx, "Please attach a valid image file!")
        
        # Check for URL if no attachment
        elif image_url:
            if not await self.is_valid_image_url(image_url):
                return await embed.error(ctx, "Please provide a valid image URL!")
            
            image_data = await self.download_image(image_url)
            if not image_data:
                return await embed.error(ctx, "Failed to download the image from the provided URL!")
        
        else:
            return await embed.error(ctx, "Please provide an image URL or attach an image file!")
        
        # Check image size (OpenBGRemover has limits)
        if len(image_data) > 10 * 1024 * 1024:  # 10MB limit
            return await embed.error(ctx, "Image is too large! Please use an image smaller than 10MB.")
        
        # Send loading message
        loading_msg = await embed.loading(ctx, "Removing background from your image...")

        # Remove background
        result_data = await self.remove_background_free(image_data)

        if result_data:
            # Create file from result
            file = discord.File(
                io.BytesIO(result_data),
                filename="removed_bg.png"
            )

            # Create success embed
            success_embed = discord.Embed(
                description=f"{Emojis.success} {ctx.author.mention}: Background removed successfully!",
                color=0xa4ec7c
            )
            success_embed.set_footer(text="Powered by Free API")

            # Edit loading message and send result
            try:
                await loading_msg.edit(embed=success_embed)
                await ctx.send(file=file)
            except Exception:
                # If edit fails, send new message
                await ctx.send(embed=success_embed, file=file)
        else:
            # Error removing background - edit the loading message instead of sending new one
            try:
                error_embed = discord.Embed(
                    description=f"{Emojis.error} {ctx.author.mention}: Failed to remove background! Please try again with a different image.",
                    color=0xff6b6b
                )
                await loading_msg.edit(embed=error_embed)
            except Exception:
                # If edit fails, send new error message
                await embed.error(ctx, "Failed to remove background! Please try again with a different image.")


async def setup(bot):
    await bot.add_cog(RemoveBG(bot))