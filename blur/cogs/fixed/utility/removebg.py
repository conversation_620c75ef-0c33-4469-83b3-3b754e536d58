import discord
from discord.ext import commands
from tools.checks import Perms
from tools.ext import embed
from config.constants import Emojis
from cogs.old.auth import PIXIAN_API_KEY
import aiohttp
import io
import re


class RemoveBG(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    async def is_valid_image_url(self, url):
        """Check if URL is a valid image"""
        image_extensions = ('.png', '.jpg', '.jpeg', '.gif', '.webp', '.bmp')
        return any(url.lower().endswith(ext) for ext in image_extensions)

    async def download_image(self, url):
        """Download image from URL"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        return await response.read()
                    else:
                        return None
        except Exception:
            return None

    async def remove_background_pixian(self, image_data):
        """Remove background using Pixian.AI API"""
        try:
            async with aiohttp.ClientSession() as session:
                # Prepare the form data
                data = aiohttp.FormData()
                data.add_field('image', image_data, filename='image.png', content_type='image/png')
                
                # Pixian.AI API endpoint
                url = 'https://api.pixian.ai/api/v2/remove-background'
                
                # Headers with API key
                headers = {
                    'Authorization': f'Bearer {PIXIAN_API_KEY}'
                }
                
                async with session.post(url, data=data, headers=headers) as response:
                    if response.status == 200:
                        return await response.read()
                    else:
                        error_text = await response.text()
                        print(f"Pixian API Error: {response.status} - {error_text}")
                        return None
        except Exception as e:
            print(f"Error removing background: {e}")
            return None

    @commands.command(
        name="removebg",
        aliases=["rbg"],
        description="Remove background from an image",
        usage="[image_url or attachment]"
    )
    @Perms.get_perms("send_messages")
    async def removebg(self, ctx, image_url: str = None):
        """Remove background from an image using Pixian.AI"""
        
        # Check for API key
        if PIXIAN_API_KEY == 'your_pixian_api_key':
            return await embed.error(ctx, "Pixian.AI API key not configured!")
        
        image_data = None
        
        # Check for attachment first
        if ctx.message.attachments:
            attachment = ctx.message.attachments[0]
            if attachment.content_type and attachment.content_type.startswith('image/'):
                try:
                    image_data = await attachment.read()
                except Exception:
                    return await embed.error(ctx, "Failed to read the attached image!")
            else:
                return await embed.error(ctx, "Please attach a valid image file!")
        
        # Check for URL if no attachment
        elif image_url:
            if not await self.is_valid_image_url(image_url):
                return await embed.error(ctx, "Please provide a valid image URL!")
            
            image_data = await self.download_image(image_url)
            if not image_data:
                return await embed.error(ctx, "Failed to download the image from the provided URL!")
        
        else:
            return await embed.error(ctx, "Please provide an image URL or attach an image file!")
        
        # Check image size (Pixian has limits)
        if len(image_data) > 10 * 1024 * 1024:  # 10MB limit
            return await embed.error(ctx, "Image is too large! Please use an image smaller than 10MB.")
        
        # Send loading message
        loading_msg = await embed.loading(ctx, "Removing background from your image...")
        
        # Remove background
        result_data = await self.remove_background_pixian(image_data)
        
        if result_data:
            # Create file from result
            file = discord.File(
                io.BytesIO(result_data), 
                filename="removed_bg.png"
            )
            
            # Create success embed
            success_embed = discord.Embed(
                description=f"{Emojis.success} {ctx.author.mention}: Background removed successfully!",
                color=0xa4ec7c
            )
            success_embed.set_footer(text="Powered by Pixian.AI")
            
            # Edit loading message and send result
            try:
                await loading_msg.edit(embed=success_embed)
                await ctx.send(file=file)
            except Exception:
                # If edit fails, send new message
                await ctx.send(embed=success_embed, file=file)
        else:
            # Error removing background
            await embed.error(ctx, "Failed to remove background! Please try again with a different image.")


async def setup(bot):
    await bot.add_cog(RemoveBG(bot))
